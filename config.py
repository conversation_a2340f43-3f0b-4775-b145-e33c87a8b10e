#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pathlib import Path

# إعدادات التطبيق
APP_NAME = "Audio Recorder Pro"
APP_VERSION = "2.0.0"
APP_AUTHOR = "Audio Recorder Team"

# إعدادات الصوت الافتراضية
DEFAULT_SAMPLE_RATE = 44100
DEFAULT_CHANNELS = 2
DEFAULT_FORMAT = "wav"

# إعدادات المجلدات
HOME_DIR = Path.home()
DEFAULT_SAVE_DIR = HOME_DIR / "Music" / "AudioRecorder"
CONFIG_DIR = HOME_DIR / ".audiorecorder"
TEMP_DIR = CONFIG_DIR / "temp"

# إنشاء المجلدات إذا لم تكن موجودة
DEFAULT_SAVE_DIR.mkdir(parents=True, exist_ok=True)
CONFIG_DIR.mkdir(parents=True, exist_ok=True)
TEMP_DIR.mkdir(parents=True, exist_ok=True)

# إعدادات الواجهة
WINDOW_MIN_WIDTH = 700
WINDOW_MIN_HEIGHT = 600
WINDOW_ICON = "assets/icon.png"

# إعدادات الألوان والأنماط
COLORS = {
    'primary': '#2196F3',
    'secondary': '#FFC107',
    'success': '#4CAF50',
    'danger': '#F44336',
    'warning': '#FF9800',
    'info': '#00BCD4',
    'light': '#F5F5F5',
    'dark': '#212121',
    'background': '#FAFAFA',
    'surface': '#FFFFFF'
}

# أنماط CSS للواجهة
STYLES = {
    'main_window': f"""
        QMainWindow {{
            background-color: {COLORS['background']};
            color: {COLORS['dark']};
        }}
    """,
    
    'tab_widget': f"""
        QTabWidget::pane {{
            border: 1px solid {COLORS['light']};
            background-color: {COLORS['surface']};
            border-radius: 8px;
        }}
        QTabBar::tab {{
            background-color: {COLORS['light']};
            color: {COLORS['dark']};
            padding: 8px 16px;
            margin: 2px;
            border-radius: 4px;
        }}
        QTabBar::tab:selected {{
            background-color: {COLORS['primary']};
            color: white;
        }}
        QTabBar::tab:hover {{
            background-color: {COLORS['info']};
            color: white;
        }}
    """,
    
    'primary_button': f"""
        QPushButton {{
            background-color: {COLORS['primary']};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }}
        QPushButton:hover {{
            background-color: #1976D2;
        }}
        QPushButton:pressed {{
            background-color: #0D47A1;
        }}
        QPushButton:disabled {{
            background-color: {COLORS['light']};
            color: #999;
        }}
    """,
    
    'secondary_button': f"""
        QPushButton {{
            background-color: {COLORS['secondary']};
            color: {COLORS['dark']};
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 11px;
        }}
        QPushButton:hover {{
            background-color: #FFB300;
        }}
        QPushButton:pressed {{
            background-color: #FF8F00;
        }}
    """,
    
    'success_button': f"""
        QPushButton {{
            background-color: {COLORS['success']};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }}
        QPushButton:hover {{
            background-color: #388E3C;
        }}
        QPushButton:pressed {{
            background-color: #2E7D32;
        }}
    """,
    
    'danger_button': f"""
        QPushButton {{
            background-color: {COLORS['danger']};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }}
        QPushButton:hover {{
            background-color: #D32F2F;
        }}
        QPushButton:pressed {{
            background-color: #C62828;
        }}
    """,
    
    'input_field': f"""
        QLineEdit {{
            border: 2px solid {COLORS['light']};
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            background-color: {COLORS['surface']};
        }}
        QLineEdit:focus {{
            border-color: {COLORS['primary']};
        }}
    """,
    
    'combo_box': f"""
        QComboBox {{
            border: 2px solid {COLORS['light']};
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            background-color: {COLORS['surface']};
        }}
        QComboBox:focus {{
            border-color: {COLORS['primary']};
        }}
        QComboBox::drop-down {{
            border: none;
        }}
        QComboBox::down-arrow {{
            image: url(assets/arrow-down.png);
            width: 12px;
            height: 12px;
        }}
    """,
    
    'group_box': f"""
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {COLORS['light']};
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: {COLORS['surface']};
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: {COLORS['primary']};
        }}
    """,
    
    'progress_bar': f"""
        QProgressBar {{
            border: 2px solid {COLORS['light']};
            border-radius: 6px;
            text-align: center;
            background-color: {COLORS['surface']};
        }}
        QProgressBar::chunk {{
            background-color: {COLORS['success']};
            border-radius: 4px;
        }}
    """,
    
    'status_bar': f"""
        QStatusBar {{
            background-color: {COLORS['surface']};
            border-top: 1px solid {COLORS['light']};
            color: {COLORS['dark']};
        }}
    """
}

# إعدادات الصيغ المدعومة
SUPPORTED_AUDIO_FORMATS = {
    'wav': 'WAV (غير مضغوط)',
    'mp3': 'MP3 (مضغوط)',
    'ogg': 'OGG (مضغوط)',
    'flac': 'FLAC (مضغوط بدون فقدان)',
    'm4a': 'M4A (مضغوط)',
    'aac': 'AAC (مضغوط)'
}

# إعدادات جودة الصوت
AUDIO_QUALITY_PRESETS = {
    'low': {'bitrate': '96k', 'sample_rate': 22050},
    'medium': {'bitrate': '128k', 'sample_rate': 44100},
    'high': {'bitrate': '192k', 'sample_rate': 44100},
    'very_high': {'bitrate': '320k', 'sample_rate': 48000}
}

# إعدادات التحميل
DOWNLOAD_SETTINGS = {
    'max_retries': 3,
    'timeout': 30,
    'chunk_size': 1024
}
