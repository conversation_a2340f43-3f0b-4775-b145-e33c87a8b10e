# تطبيق مسجل الصوت

تطبيق سطح مكتب بلغة Python باستخدام PyQt5 لتسجيل الصوت وتحميل الملفات الصوتية من روابط مثل YouTube.

## المميزات

- واجهة رسومية سهلة الاستخدام
- دعم اللغتين العربية والإنجليزية
- تسجيل الصوت من الميكروفون
- تحميل الصوت أو الفيديو من روابط (مثل YouTube)
- حفظ التسجيلات بصيغة WAV أو MP3
- تحويل الملفات الصوتية بين صيغ مختلفة (MP3، WAV، OGG، FLAC)
- عرض معلومات الملف (المدة، الحجم، الصيغة)
- إشعارات عند اكتمال التسجيل أو التحميل
- عمليات غير متزامنة لمنع تجميد الواجهة

## متطلبات التشغيل

- Python 3.6 أو أحدث
- المكتبات التالية:
  - PyQt5
  - sounddevice
  - soundfile
  - yt-dlp
  - pydub
- ffmpeg (مطلوب لتحويل الصيغ الصوتية)

## طريقة التثبيت

1. تأكد من تثبيت Python 3.6 أو أحدث
2. قم بتثبيت المكتبات المطلوبة:

```bash
pip install PyQt5 sounddevice soundfile yt-dlp pydub
```

3. قم بتثبيت ffmpeg حسب نظام التشغيل:

- **Ubuntu/Debian**:
```bash
sudo apt-get install ffmpeg
```

- **Windows**:
يمكن تحميله من الموقع الرسمي: https://ffmpeg.org/download.html

- **macOS**:
```bash
brew install ffmpeg
```

4. قم بتشغيل التطبيق:

```bash
python main.py
```

## طريقة الاستخدام

### تسجيل الصوت

1. انتقل إلى تبويب "تسجيل الصوت"
2. اختر مصدر التسجيل (الميكروفون)
3. اختر صيغة الصوت (WAV أو MP3)
4. اختر مجلد الحفظ عن طريق زر "تصفح"
5. اضغط على زر "ابدأ التسجيل" لبدء التسجيل
6. اضغط على نفس الزر (الذي سيتغير إلى "إيقاف التسجيل") لإيقاف التسجيل
7. بعد الانتهاء، ستظهر معلومات الملف المسجل

### تحميل من رابط

1. انتقل إلى تبويب "تحميل من رابط"
2. أدخل رابط الفيديو أو الصوت (مثل رابط YouTube)
3. اختر نوع التحميل (صوت فقط أو فيديو مع صوت)
4. اختر مجلد الحفظ عن طريق زر "تصفح"
5. اضغط على زر "تحميل من رابط" لبدء التحميل
6. انتظر حتى اكتمال التحميل، وستظهر معلومات الملف المحمل

### تحويل الصيغ الصوتية

1. بعد تسجيل صوت أو تحميل ملف، ستظهر معلومات الملف
2. اختر الصيغة المطلوبة من قائمة "تحويل إلى"
3. اضغط على زر "تحويل"
4. انتظر حتى اكتمال التحويل، وستظهر معلومات الملف الجديد

### تغيير اللغة

- يمكنك تغيير لغة التطبيق من القائمة المنسدلة في أعلى التطبيق (العربية/English)

## ملاحظات هامة

- تسجيل صوت النظام مباشرة قد يتطلب إعدادات إضافية حسب نظام التشغيل
- للحصول على أفضل جودة صوت، تأكد من إعدادات الميكروفون في نظام التشغيل
- بعض روابط الفيديو قد تكون محمية ولا يمكن تحميلها
- يتطلب التطبيق اتصال بالإنترنت لتحميل الملفات من الروابط

## المطورون

تم تطوير هذا التطبيق استجابة لطلب المستخدم لإنشاء تطبيق سطح مكتب لتسجيل الصوت وتحميل الملفات الصوتية.
