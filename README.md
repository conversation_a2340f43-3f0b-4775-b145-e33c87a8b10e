# 🎵 Audio Recorder Pro - مسجل الصوت المحترف

<div align="center">

![Audio Recorder Pro](assets/icon.png)

**تطبيق احترافي لتسجيل الصوت وتحميل الملفات الصوتية**

[![Python](https://img.shields.io/badge/Python-3.6+-blue.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)]()

</div>

## 📖 نظرة عامة

Audio Recorder Pro هو تطبيق سطح مكتب متقدم مكتوب بلغة Python باستخدام PyQt5، يوفر حلولاً شاملة لتسجيل الصوت وتحميل الملفات الصوتية من مختلف المصادر مثل YouTube وSoundCloud.

## 🎯 الاستخدام السريع

### تسجيل صوت سريع
1. شغل التطبيق بالنقر على `run.bat` (Windows) أو `python3 run.py`
2. اختر "الميكروفون" كمصدر التسجيل
3. اضغط "ابدأ التسجيل"
4. تحدث في الميكروفون
5. اضغط "إيقاف التسجيل" عند الانتهاء

### تحميل من YouTube
1. انتقل لتبويب "تحميل من رابط"
2. الصق رابط الفيديو من YouTube
3. اختر "صوت فقط" لتحميل الصوت فقط
4. اضغط "تحميل من رابط" وانتظر الانتهاء

## ✨ المميزات الرئيسية

### 🎙️ تسجيل الصوت المتقدم
- **تسجيل عالي الجودة** من الميكروفون أو صوت النظام
- **دعم صيغ متعددة**: WAV، MP3، OGG، FLAC، M4A، AAC
- **إعدادات جودة قابلة للتخصيص** (96k إلى 320k bitrate)
- **مراقبة الوقت الفعلي** مع شريط تقدم تفاعلي

### 📥 تحميل من المصادر المختلفة
- **دعم YouTube وSoundCloud** وروابط الصوت المباشرة
- **خيارات تحميل مرنة**: صوت فقط أو فيديو مع صوت
- **تحميل متوازي** مع مؤشر تقدم
- **استئناف التحميل** في حالة انقطاع الاتصال

### 🔄 تحويل الصيغ الاحترافي
- **تحويل سريع** بين جميع الصيغ المدعومة
- **الحفاظ على جودة الصوت** أثناء التحويل
- **معاينة قبل التحويل** مع عرض معلومات الملف
- **تحويل مجمع** لملفات متعددة

### 🌐 واجهة مستخدم متطورة
- **تصميم عصري** مع ألوان وأنماط احترافية
- **دعم كامل للغة العربية والإنجليزية**
- **واجهة سهلة الاستخدام** مع تبويبات منظمة
- **إشعارات ذكية** لحالة العمليات

### ⚡ أداء محسن
- **عمليات غير متزامنة** لمنع تجميد الواجهة
- **إدارة ذاكرة فعالة** للملفات الكبيرة
- **تنظيف تلقائي** للملفات المؤقتة
- **مراقبة الأداء** مع إحصائيات مفصلة

### 🆕 مميزات جديدة (v2.1)
- **🎤➡️📝 تحويل الصوت إلى نص** باستخدام الذكاء الاصطناعي
- **🗑️ زر المسح** لتنظيف البيانات بسرعة
- **❌ زر الإلغاء** لإيقاف العمليات الجارية
- **📝 منطقة عرض النص** قابلة للتحرير والنسخ

## متطلبات التشغيل

- Python 3.6 أو أحدث
- المكتبات التالية:
  - PyQt5
  - sounddevice
  - soundfile
  - yt-dlp
  - pydub
- ffmpeg (مطلوب لتحويل الصيغ الصوتية)

## 🚀 التثبيت السريع

### الطريقة الأولى: التشغيل المباشر (موصى به)

1. **تحميل المشروع**:
   ```bash
   git clone https://github.com/audiorecorder/audio-recorder-pro.git
   cd audio-recorder-pro
   ```

2. **التشغيل التلقائي**:
   - **Windows**: انقر مرتين على `run.bat`
   - **Linux/macOS**:
     ```bash
     chmod +x run.py
     python3 run.py
     ```

### الطريقة الثانية: التثبيت اليدوي

1. **تأكد من تثبيت Python 3.6+**:
   ```bash
   python --version
   ```

2. **تثبيت المكتبات**:
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق**:
   ```bash
   python main.py
   ```

### الطريقة الثالثة: التثبيت كحزمة

```bash
pip install -e .
audio-recorder
```

3. قم بتثبيت ffmpeg حسب نظام التشغيل:

- **Ubuntu/Debian**:
```bash
sudo apt-get install ffmpeg
```

- **Windows**:
يمكن تحميله من الموقع الرسمي: https://ffmpeg.org/download.html

- **macOS**:
```bash
brew install ffmpeg
```

4. قم بتشغيل التطبيق:

```bash
python main.py
```

## طريقة الاستخدام

### تسجيل الصوت

1. انتقل إلى تبويب "تسجيل الصوت"
2. اختر مصدر التسجيل (الميكروفون)
3. اختر صيغة الصوت (WAV أو MP3)
4. اختر مجلد الحفظ عن طريق زر "تصفح"
5. اضغط على زر "ابدأ التسجيل" لبدء التسجيل
6. اضغط على نفس الزر (الذي سيتغير إلى "إيقاف التسجيل") لإيقاف التسجيل
7. بعد الانتهاء، ستظهر معلومات الملف المسجل

### تحميل من رابط

1. انتقل إلى تبويب "تحميل من رابط"
2. أدخل رابط الفيديو أو الصوت (مثل رابط YouTube)
3. اختر نوع التحميل (صوت فقط أو فيديو مع صوت)
4. اختر مجلد الحفظ عن طريق زر "تصفح"
5. اضغط على زر "تحميل من رابط" لبدء التحميل
6. انتظر حتى اكتمال التحميل، وستظهر معلومات الملف المحمل

### تحويل الصيغ الصوتية

1. بعد تسجيل صوت أو تحميل ملف، ستظهر معلومات الملف
2. اختر الصيغة المطلوبة من قائمة "تحويل إلى"
3. اضغط على زر "تحويل"
4. انتظر حتى اكتمال التحويل، وستظهر معلومات الملف الجديد

### تغيير اللغة

- يمكنك تغيير لغة التطبيق من القائمة المنسدلة في أعلى التطبيق (العربية/English)

## ملاحظات هامة

- تسجيل صوت النظام مباشرة قد يتطلب إعدادات إضافية حسب نظام التشغيل
- للحصول على أفضل جودة صوت، تأكد من إعدادات الميكروفون في نظام التشغيل
- بعض روابط الفيديو قد تكون محمية ولا يمكن تحميلها
- يتطلب التطبيق اتصال بالإنترنت لتحميل الملفات من الروابط

## المطورون

تم تطوير هذا التطبيق استجابة لطلب المستخدم لإنشاء تطبيق سطح مكتب لتسجيل الصوت وتحميل الملفات الصوتية.
