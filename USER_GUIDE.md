# 📖 دليل المستخدم - Audio Recorder Pro

## 🚀 البدء السريع

### تشغيل التطبيق

#### Windows
1. انقر مرتين على ملف `run.bat`
2. انتظر حتى يتم تثبيت المكتبات تلقائياً
3. سيفتح التطبيق تلقائياً

#### Linux/macOS
```bash
python3 run.py
```

### الواجهة الرئيسية

التطبيق يحتوي على تبويبين رئيسيين:
- **تسجيل الصوت**: لتسجيل الصوت من الميكروفون أو النظام
- **تحميل من رابط**: لتحميل الصوت من YouTube وروابط أخرى

---

## 🎙️ تسجيل الصوت

### الخطوات الأساسية

1. **اختر مصدر التسجيل**:
   - **الميكروفون**: لتسجيل صوتك
   - **صوت النظام**: لتسجيل ما يشغله الكمبيوتر

2. **اختر صيغة الصوت**:
   - **WAV**: جودة عالية، حجم كبير
   - **MP3**: جودة جيدة، حجم متوسط

3. **حدد مكان الحفظ**:
   - اضغط "تصفح" لاختيار المجلد
   - المجلد الافتراضي: `Music/AudioRecorder`

4. **ابدأ التسجيل**:
   - اضغط "ابدأ التسجيل"
   - راقب الوقت في شريط التقدم
   - اضغط "إيقاف التسجيل" عند الانتهاء

### نصائح للتسجيل

- **تأكد من جودة الميكروفون** قبل البدء
- **اختبر مستوى الصوت** في إعدادات النظام
- **تجنب الضوضاء** في البيئة المحيطة
- **استخدم سماعات** لتجنب التداخل

---

## 📥 تحميل من الروابط

### الروابط المدعومة

- **YouTube**: `https://youtube.com/watch?v=...`
- **YouTube Short**: `https://youtu.be/...`
- **SoundCloud**: `https://soundcloud.com/...`
- **روابط مباشرة**: ملفات `.mp3`, `.wav`, `.ogg`

### خطوات التحميل

1. **أدخل الرابط**:
   - انسخ رابط الفيديو/الصوت
   - الصقه في حقل "أدخل رابط الفيديو/الصوت"

2. **اختر نوع التحميل**:
   - **صوت فقط**: لتحميل الصوت بدون فيديو
   - **فيديو مع صوت**: لتحميل الفيديو كاملاً

3. **حدد مكان الحفظ**:
   - اضغط "تصفح" لاختيار المجلد

4. **ابدأ التحميل**:
   - اضغط "تحميل من رابط"
   - راقب التقدم في الشريط
   - انتظر حتى اكتمال التحميل

### نصائح للتحميل

- **تأكد من اتصال الإنترنت** المستقر
- **تحقق من صحة الرابط** قبل التحميل
- **اختر جودة مناسبة** حسب الحاجة
- **احترم حقوق الطبع والنشر**

---

## 🔄 تحويل الصيغ

### الصيغ المدعومة

- **MP3**: الأكثر شيوعاً، متوافق مع جميع الأجهزة
- **WAV**: جودة عالية، حجم كبير
- **OGG**: جودة جيدة، حجم صغير
- **FLAC**: جودة عالية بدون فقدان
- **M4A**: جودة جيدة، متوافق مع Apple
- **AAC**: جودة جيدة، حجم صغير

### خطوات التحويل

1. **سجل أو حمل ملف صوتي** أولاً
2. **ستظهر معلومات الملف** تلقائياً
3. **اختر الصيغة المطلوبة** من قائمة "تحويل إلى"
4. **اضغط "تحويل"** وانتظر الانتهاء

### نصائح للتحويل

- **MP3 للاستخدام العام**: متوافق مع جميع الأجهزة
- **FLAC للجودة العالية**: للموسيقى الاحترافية
- **OGG للحجم الصغير**: لتوفير المساحة
- **WAV للتحرير**: للمونتاج والتحرير

---

## ⚙️ الإعدادات والتخصيص

### تغيير اللغة

- اختر من القائمة المنسدلة في أعلى التطبيق
- **العربية**: للواجهة العربية
- **English**: للواجهة الإنجليزية

### إعدادات الجودة

يمكن تعديل إعدادات الجودة في ملف `app_config.ini`:

```ini
[Audio]
default_sample_rate = 44100  # جودة الصوت
default_channels = 2         # ستيريو
default_format = wav         # الصيغة الافتراضية
```

### مجلدات الحفظ

- **الافتراضي**: `~/Music/AudioRecorder`
- **مؤقت**: `~/.audiorecorder/temp`
- **إعدادات**: `~/.audiorecorder`

---

## 🔧 حل المشاكل الشائعة

### مشاكل التسجيل

**المشكلة**: لا يتم تسجيل صوت
- **الحل**: تحقق من إعدادات الميكروفون في النظام
- **الحل**: جرب تغيير مصدر التسجيل

**المشكلة**: جودة صوت ضعيفة
- **الحل**: اختر صيغة WAV بدلاً من MP3
- **الحل**: تحقق من جودة الميكروفون

### مشاكل التحميل

**المشكلة**: فشل في التحميل
- **الحل**: تحقق من اتصال الإنترنت
- **الحل**: تأكد من صحة الرابط
- **الحل**: جرب رابط آخر

**المشكلة**: تحميل بطيء
- **الحل**: تحقق من سرعة الإنترنت
- **الحل**: أغلق التطبيقات الأخرى
- **الحل**: جرب في وقت آخر

### مشاكل التحويل

**المشكلة**: فشل في التحويل
- **الحل**: تأكد من تثبيت ffmpeg
- **الحل**: تحقق من مساحة القرص الصلب
- **الحل**: جرب صيغة أخرى

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة

- **الأسئلة الشائعة**: راجع هذا الدليل
- **الإبلاغ عن مشاكل**: [GitHub Issues](https://github.com/audiorecorder/issues)
- **طلب مميزات**: [GitHub Discussions](https://github.com/audiorecorder/discussions)

### معلومات النظام

لمساعدتنا في حل المشاكل، يرجى تقديم:
- نظام التشغيل وإصداره
- إصدار Python
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة

---

## 🎯 نصائح متقدمة

### تحسين الأداء

- **أغلق التطبيقات غير الضرورية** أثناء التسجيل
- **استخدم قرص SSD** لتحسين سرعة الكتابة
- **نظف الملفات المؤقتة** بانتظام

### الاستخدام الاحترافي

- **استخدم ميكروفون خارجي** للجودة العالية
- **سجل في بيئة هادئة** لتجنب الضوضاء
- **احفظ نسخ احتياطية** من التسجيلات المهمة

### التخصيص المتقدم

- **عدل ملف config.py** للإعدادات المتقدمة
- **استخدم ملف app_config.ini** للتخصيص
- **أضف أنماط CSS** مخصصة للواجهة

---

**شكراً لاستخدام Audio Recorder Pro! 🎵**
