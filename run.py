#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Audio Recorder Pro - تطبيق مسجل الصوت المحترف
ملف التشغيل الرئيسي للتطبيق
"""

import sys
import os
import traceback
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """التحقق من توفر المكتبات المطلوبة"""
    missing_modules = []
    required_modules = {
        'PyQt5': 'PyQt5',
        'sounddevice': 'sounddevice',
        'soundfile': 'soundfile',
        'numpy': 'numpy',
        'yt_dlp': 'yt-dlp',
        'pydub': 'pydub'
    }
    
    for module_name, package_name in required_modules.items():
        try:
            __import__(module_name)
        except ImportError:
            missing_modules.append(package_name)
    
    if missing_modules:
        print("❌ المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n📦 لتثبيت المكتبات المطلوبة، قم بتشغيل:")
        print("pip install -r requirements.txt")
        print("\nأو:")
        print("pip install " + " ".join(missing_modules))
        return False
    
    print("✅ جميع المكتبات المطلوبة متوفرة")
    return True

def check_ffmpeg():
    """التحقق من توفر ffmpeg"""
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ffmpeg متوفر")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️  ffmpeg غير متوفر - قد تواجه مشاكل في تحويل الصيغ")
    print("لتثبيت ffmpeg:")
    print("- Windows: قم بتحميله من https://ffmpeg.org/download.html")
    print("- Ubuntu/Debian: sudo apt-get install ffmpeg")
    print("- macOS: brew install ffmpeg")
    return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🎵 Audio Recorder Pro - مسجل الصوت المحترف")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_dependencies():
        input("\nاضغط Enter للخروج...")
        return 1
    
    check_ffmpeg()
    
    try:
        # استيراد وتشغيل التطبيق
        print("\n🚀 بدء تشغيل التطبيق...")
        from main import AudioRecorderApp
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # تعيين نمط الخط للدعم العربي
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # إنشاء النافذة الرئيسية
        window = AudioRecorderApp()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("💡 يمكنك الآن استخدام التطبيق")
        
        # تشغيل حلقة الأحداث
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("تأكد من تثبيت جميع المكتبات المطلوبة")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ فادح: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
