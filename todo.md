# قائمة المهام لتطبيق تسجيل الصوت

## المتطلبات الأساسية
- [x] جمع متطلبات المشروع والتأكد من جميع التفاصيل
- [x] إعداد بيئة العمل وتثبيت المكتبات اللازمة
  - [x] تثبيت PyQt5 (واجهة المستخدم الرسومية)
  - [x] تثبيت sounddevice (لتسجيل الصوت)
  - [x] تثبيت soundfile (للتعامل مع ملفات الصوت)
  - [x] تثبيت yt_dlp (لتحميل الصوت/الفيديو من روابط)
  - [x] تثبيت pydub وffmpeg (للتحويل بين صيغ الصوت)

## تطوير التطبيق
- [x] تصميم الواجهة الرسومية ثنائية اللغة (عربية/إنجليزية)
  - [x] إنشاء الأزرار والعناصر المطلوبة
  - [x] إضافة دعم اللغة العربية والإنجليزية
- [x] برمجة وظيفة تسجيل الصوت من النظام
  - [x] تنفيذ زر بدء التسجيل
  - [x] تنفيذ زر إيقاف التسجيل
  - [x] حفظ الملف بصيغة .wav أو .mp3
- [x] برمجة وظيفة تحميل الصوت/الفيديو من رابط
  - [x] تنفيذ زر تحميل من رابط
  - [x] دعم اختيار مجلد الحفظ
- [x] إضافة شريط حالة أو مؤشر تقدم
- [x] تنفيذ العمليات في خيوط منفصلة (QThread)
- [x] إضافة إشعارات عند اكتمال التسجيل أو التحميل

## الميزات الإضافية (اختيارية)
- [x] دعم تحويل الصوت لصيغ مختلفة
- [x] عرض معلومات الملف (المدة، الحجم)
- [x] إضافة زر لفتح المجلد

## الاختبار والتسليم
- [ ] اختبار جميع الوظائف
- [ ] التحقق من دعم اللغتين
- [ ] كتابة ملاحظات الاستخدام
- [ ] تسليم التطبيق النهائي للمستخدم


- [ ] إضافة زر لفتح المجلد

## الاختبار والتسليم
- [ ] اختبار جميع الوظائف
- [ ] التحقق من دعم اللغتين
- [ ] كتابة ملاحظات الاستخدام
- [ ] تسليم التطبيق النهائي للمستخدم
