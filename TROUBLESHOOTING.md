# 🔧 دليل حل المشاكل - Audio Recorder Pro

## 🎯 نظرة عامة

هذا الدليل يساعدك في حل المشاكل الشائعة التي قد تواجهها أثناء استخدام Audio Recorder Pro.

## 🚨 المشاكل الشائعة وحلولها

### 1. 🎤➡️📝 مشاكل تحويل الصوت إلى نص

#### المشكلة: "مكتبات تحويل الصوت إلى نص غير متوفرة"
**الأعراض**: رسالة خطأ عند الضغط على "تحويل إلى نص"

**الحلول**:

**الحل الأول (تلقائي)**:
1. اضغط "نعم" في نافذة التثبيت التلقائي
2. انتظر حتى اكتمال التثبيت
3. أعد تشغيل التطبيق

**الحل الثاني (يدوي)**:
```bash
# تثبيت SpeechRecognition
pip install SpeechRecognition pyaudio

# أو تثبيت Whisper (أفضل دقة)
pip install openai-whisper torch torchaudio
```

**الحل الثالث (مساعد التثبيت)**:
```bash
python install_dependencies.py
```

#### المشكلة: تحويل بطيء أو فاشل
**الأعراض**: التحويل يستغرق وقت طويل أو يفشل

**الحلول**:
- **تحقق من جودة التسجيل**: تأكد من وضوح الصوت
- **تحقق من الاتصال**: SpeechRecognition يحتاج إنترنت
- **جرب ملف أقصر**: ابدأ بتسجيل قصير للاختبار
- **استخدم Whisper**: أفضل للملفات الطويلة

#### المشكلة: النص غير دقيق
**الأعراض**: النص المستخرج مختلف عن المحتوى الصوتي

**الحلول**:
- **حسن جودة التسجيل**: استخدم ميكروفون أفضل
- **تحدث بوضوح**: تجنب السرعة المفرطة
- **قلل الضوضاء**: سجل في مكان هادئ
- **استخدم لغة واحدة**: لا تخلط العربية والإنجليزية

### 2. 🎙️ مشاكل التسجيل

#### المشكلة: لا يتم تسجيل صوت
**الأعراض**: التسجيل يعمل لكن الملف فارغ أو صامت

**الحلول**:
- **تحقق من الميكروفون**: تأكد من توصيله وتشغيله
- **تحقق من الأذونات**: امنح التطبيق إذن الوصول للميكروفون
- **جرب مصدر آخر**: غير من "الميكروفون" إلى "صوت النظام"
- **تحقق من مستوى الصوت**: ارفع مستوى الميكروفون في النظام

#### المشكلة: جودة صوت ضعيفة
**الأعراض**: الصوت مشوش أو غير واضح

**الحلول**:
- **اختر صيغة WAV**: بدلاً من MP3 للجودة العالية
- **تحقق من إعدادات الميكروفون**: في إعدادات النظام
- **استخدم ميكروفون خارجي**: أفضل من الميكروفون المدمج
- **تجنب الضوضاء**: سجل في مكان هادئ

### 3. 📥 مشاكل التحميل

#### المشكلة: فشل في التحميل من YouTube
**الأعراض**: رسالة خطأ عند محاولة التحميل

**الحلول**:
- **تحقق من الرابط**: تأكد من صحة رابط YouTube
- **تحقق من الاتصال**: تأكد من اتصال الإنترنت
- **جرب رابط آخر**: قد يكون الفيديو محمي
- **حدث yt-dlp**: `pip install --upgrade yt-dlp`

#### المشكلة: تحميل بطيء
**الأعراض**: التحميل يستغرق وقت طويل

**الحلول**:
- **تحقق من سرعة الإنترنت**: استخدم اتصال أسرع
- **أغلق التطبيقات الأخرى**: التي تستخدم الإنترنت
- **اختر جودة أقل**: إذا كان متاح
- **جرب في وقت آخر**: قد تكون الخوادم مزدحمة

### 4. 🔄 مشاكل تحويل الصيغ

#### المشكلة: فشل في تحويل الصيغة
**الأعراض**: رسالة خطأ عند محاولة التحويل

**الحلول**:
- **تثبيت ffmpeg**: مطلوب لتحويل الصيغ
  ```bash
  # Windows: حمل من https://ffmpeg.org
  # Ubuntu: sudo apt install ffmpeg
  # macOS: brew install ffmpeg
  ```
- **تحقق من مساحة القرص**: تأكد من وجود مساحة كافية
- **جرب صيغة أخرى**: قد تكون الصيغة المطلوبة غير مدعومة

### 5. 🖥️ مشاكل الواجهة

#### المشكلة: النص العربي يظهر بشكل خاطئ
**الأعراض**: أحرف مربعات أو نص مقلوب

**الحلول**:
- **تثبيت خطوط عربية**: تأكد من وجود خطوط عربية في النظام
- **تغيير اللغة**: جرب تغيير لغة النظام
- **إعادة تشغيل**: أعد تشغيل التطبيق

#### المشكلة: التطبيق يتجمد
**الأعراض**: التطبيق لا يستجيب

**الحلول**:
- **انتظر قليلاً**: قد تكون العملية تحتاج وقت
- **أغلق وأعد فتح**: أغلق التطبيق وافتحه مرة أخرى
- **تحقق من الذاكرة**: أغلق التطبيقات الأخرى
- **أعد تشغيل النظام**: في الحالات الصعبة

## 🛠️ أدوات التشخيص

### فحص المكتبات
```bash
python install_dependencies.py
```

### اختبار التطبيق
```bash
python test_app.py
```

### فحص النظام
```bash
python -c "import platform; print(platform.platform())"
```

## 📋 معلومات النظام المطلوبة

عند طلب المساعدة، يرجى تقديم:

### معلومات النظام
- نظام التشغيل وإصداره
- إصدار Python: `python --version`
- إصدار التطبيق: موجود في config.py

### معلومات الخطأ
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- ما كنت تحاول فعله

### ملفات السجل
- لقطة شاشة للخطأ
- محتوى terminal/command prompt

## 🔍 تشخيص متقدم

### فحص المكتبات يدوياً
```python
# فحص PyQt5
try:
    from PyQt5.QtWidgets import QApplication
    print("✅ PyQt5 يعمل")
except ImportError:
    print("❌ PyQt5 مفقود")

# فحص تسجيل الصوت
try:
    import sounddevice as sd
    print("✅ sounddevice يعمل")
    print(f"الأجهزة المتاحة: {sd.query_devices()}")
except ImportError:
    print("❌ sounddevice مفقود")

# فحص تحويل الصوت إلى نص
try:
    import speech_recognition as sr
    print("✅ SpeechRecognition يعمل")
except ImportError:
    print("❌ SpeechRecognition مفقود")
```

### فحص ffmpeg
```bash
ffmpeg -version
```

## 📞 طلب المساعدة

### قنوات الدعم
- **GitHub Issues**: للمشاكل التقنية
- **GitHub Discussions**: للأسئلة العامة
- **البريد الإلكتروني**: <EMAIL>

### قبل طلب المساعدة
1. **جرب الحلول المذكورة**: في هذا الدليل
2. **ابحث في Issues**: قد تكون المشكلة محلولة
3. **اجمع المعلومات**: المطلوبة أعلاه
4. **اكتب وصف واضح**: للمشكلة

## 🎯 نصائح لتجنب المشاكل

### للتسجيل
- **اختبر الميكروفون**: قبل التسجيل المهم
- **تحقق من المساحة**: تأكد من وجود مساحة كافية
- **استخدم مجلد مناسب**: تجنب مجلدات النظام

### للتحميل
- **تحقق من الرابط**: قبل بدء التحميل
- **استخدم اتصال مستقر**: تجنب WiFi الضعيف
- **احفظ في مكان آمن**: تجنب فقدان الملفات

### للتحويل
- **احفظ نسخة احتياطية**: قبل التحويل
- **تأكد من ffmpeg**: قبل استخدام التحويل
- **جرب على ملف صغير**: أولاً

---

**نأمل أن يساعدك هذا الدليل في حل مشاكلك! 🛠️✨**

*إذا لم تجد حلاً لمشكلتك، لا تتردد في طلب المساعدة*
