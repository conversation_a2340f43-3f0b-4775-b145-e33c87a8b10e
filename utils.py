#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QTimer
from config import CONFIG_DIR

def format_duration(seconds):
    """تحويل الثواني إلى تنسيق مقروء"""
    if seconds < 60:
        return f"{seconds:.1f} ثانية"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{int(minutes)} دقيقة و {remaining_seconds:.1f} ثانية"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{int(hours)} ساعة و {int(remaining_minutes)} دقيقة"

def format_file_size(size_bytes):
    """تحويل حجم الملف إلى تنسيق مقروء"""
    if size_bytes < 1024:
        return f"{size_bytes} بايت"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} كيلوبايت"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} جيجابايت"

def get_audio_info(file_path):
    """الحصول على معلومات الملف الصوتي"""
    try:
        import soundfile as sf
        
        if not os.path.exists(file_path):
            return None
            
        info = sf.info(file_path)
        file_size = os.path.getsize(file_path)
        
        return {
            'duration': info.duration,
            'sample_rate': info.samplerate,
            'channels': info.channels,
            'format': info.format,
            'subtype': info.subtype,
            'size': file_size,
            'duration_formatted': format_duration(info.duration),
            'size_formatted': format_file_size(file_size)
        }
    except Exception as e:
        print(f"خطأ في الحصول على معلومات الملف: {e}")
        return None

def validate_url(url):
    """التحقق من صحة الرابط"""
    import re
    
    # أنماط الروابط المدعومة
    patterns = [
        r'https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+',
        r'https?://(?:www\.)?youtu\.be/[\w-]+',
        r'https?://(?:www\.)?soundcloud\.com/[\w-]+/[\w-]+',
        r'https?://(?:www\.)?vimeo\.com/\d+',
        r'https?://.*\.(mp3|wav|ogg|flac|m4a|aac)$'
    ]
    
    for pattern in patterns:
        if re.match(pattern, url, re.IGNORECASE):
            return True
    
    return False

def generate_filename(prefix="recording", extension="wav"):
    """إنشاء اسم ملف فريد"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.{extension}"

def open_file_location(file_path):
    """فتح موقع الملف في مستكشف الملفات"""
    try:
        if sys.platform == 'win32':
            os.startfile(os.path.dirname(file_path))
        elif sys.platform == 'darwin':  # macOS
            os.system(f'open "{os.path.dirname(file_path)}"')
        else:  # Linux
            os.system(f'xdg-open "{os.path.dirname(file_path)}"')
    except Exception as e:
        print(f"خطأ في فتح موقع الملف: {e}")

def save_settings(settings):
    """حفظ إعدادات التطبيق"""
    try:
        settings_file = CONFIG_DIR / "settings.json"
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"خطأ في حفظ الإعدادات: {e}")
        return False

def load_settings():
    """تحميل إعدادات التطبيق"""
    try:
        settings_file = CONFIG_DIR / "settings.json"
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"خطأ في تحميل الإعدادات: {e}")
    
    # الإعدادات الافتراضية
    return {
        'language': 'ar',
        'save_directory': str(Path.home() / "Music" / "AudioRecorder"),
        'audio_format': 'wav',
        'audio_quality': 'high',
        'auto_open_folder': False,
        'show_notifications': True
    }

def show_notification(title, message, icon_type="info"):
    """عرض إشعار للمستخدم"""
    try:
        from plyer import notification
        notification.notify(
            title=title,
            message=message,
            timeout=5
        )
    except ImportError:
        # استخدام QMessageBox كبديل
        msg_box = QMessageBox()
        if icon_type == "info":
            msg_box.setIcon(QMessageBox.Information)
        elif icon_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
        elif icon_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
        
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

def cleanup_temp_files():
    """تنظيف الملفات المؤقتة"""
    try:
        from config import TEMP_DIR
        
        # حذف الملفات الأقدم من يوم واحد
        cutoff_time = time.time() - (24 * 60 * 60)  # 24 ساعة
        
        for file_path in TEMP_DIR.glob("*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                file_path.unlink()
                
    except Exception as e:
        print(f"خطأ في تنظيف الملفات المؤقتة: {e}")

def check_dependencies():
    """التحقق من توفر المكتبات المطلوبة"""
    required_modules = [
        'PyQt5',
        'sounddevice',
        'soundfile',
        'yt_dlp',
        'pydub',
        'numpy'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    return missing_modules

def get_system_info():
    """الحصول على معلومات النظام"""
    import platform
    
    return {
        'platform': platform.system(),
        'platform_version': platform.version(),
        'architecture': platform.architecture()[0],
        'python_version': platform.python_version(),
        'processor': platform.processor()
    }

class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.start_time = None
        self.operations = []
    
    def start_operation(self, name):
        """بدء مراقبة عملية"""
        self.start_time = time.time()
        self.current_operation = name
    
    def end_operation(self):
        """انتهاء مراقبة العملية"""
        if self.start_time:
            duration = time.time() - self.start_time
            self.operations.append({
                'name': self.current_operation,
                'duration': duration,
                'timestamp': datetime.now()
            })
            self.start_time = None
            return duration
        return 0
    
    def get_stats(self):
        """الحصول على إحصائيات الأداء"""
        if not self.operations:
            return {}
        
        durations = [op['duration'] for op in self.operations]
        return {
            'total_operations': len(self.operations),
            'average_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'total_time': sum(durations)
        }

def create_backup(file_path, backup_dir=None):
    """إنشاء نسخة احتياطية من الملف"""
    try:
        import shutil
        
        if backup_dir is None:
            backup_dir = CONFIG_DIR / "backups"
            backup_dir.mkdir(exist_ok=True)
        
        file_name = Path(file_path).name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{timestamp}_{file_name}"
        backup_path = backup_dir / backup_name
        
        shutil.copy2(file_path, backup_path)
        return str(backup_path)
        
    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None
