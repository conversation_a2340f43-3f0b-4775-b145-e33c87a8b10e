# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.1.0] - 2025-01-26

### ✨ إضافات جديدة
- **🗑️ زر المسح**: مسح جميع البيانات والمعلومات المعروضة
- **❌ زر الإلغاء**: إلغاء عملية التسجيل الجارية
- **🎤➡️📝 تحويل الصوت إلى نص**: استخراج النص من الملفات الصوتية
  - دعم OpenAI Whisper للدقة العالية
  - دعم Google Speech Recognition كبديل
  - دعم اللغة العربية والإنجليزية
- **منطقة عرض النص**: مربع نص قابل للتحرير لعرض النتائج
- **رسائل تأكيد**: نوافذ تأكيد قبل العمليات المهمة

### 🔧 تحسينات
- **واجهة محسنة**: أزرار ملونة ومنظمة أكثر
- **معالجة غير متزامنة**: لتحويل الصوت إلى نص
- **مؤشرات التقدم**: عرض حالة العمليات في شريط الحالة
- **إدارة أفضل للحالة**: تفعيل/إلغاء تفعيل الأزرار حسب الحاجة

### 📚 توثيق
- **دليل المميزات الجديدة**: NEW_FEATURES.md
- **تحديث دليل المستخدم**: إضافة شرح المميزات الجديدة
- **تحديث متطلبات التثبيت**: إضافة مكتبات تحويل الصوت

## [2.0.0] - 2025-01-26

### ✨ إضافات جديدة
- **واجهة مستخدم محسنة** مع تصميم عصري وألوان احترافية
- **نظام تكوين متقدم** مع ملفات config.py و app_config.ini
- **دوال مساعدة شاملة** في utils.py لتحسين الأداء
- **نظام تثبيت احترافي** مع setup.py و requirements.txt
- **ملف تشغيل محسن** run.py مع فحص المتطلبات التلقائي
- **ملف batch للويندوز** run.bat للتشغيل السهل
- **اختبارات تلقائية** في test_app.py للتحقق من سلامة التطبيق
- **دعم أنظمة Linux** مع ملف .desktop
- **رخصة MIT** مع ملف LICENSE
- **توثيق شامل** مع README.md محسن

### 🔧 تحسينات
- **أنماط CSS متقدمة** للأزرار والعناصر
- **إدارة أفضل للألوان** مع نظام ألوان موحد
- **تحسين الأداء** مع مراقب الأداء المدمج
- **إدارة الإعدادات** مع حفظ وتحميل تلقائي
- **تنظيف الملفات المؤقتة** التلقائي
- **إشعارات محسنة** مع دعم مكتبة plyer

### 🐛 إصلاحات
- إصلاح مشاكل الترميز العربي
- تحسين استقرار التطبيق
- إصلاح مشاكل الذاكرة
- تحسين معالجة الأخطاء

### 📚 توثيق
- README.md شامل مع أمثلة وصور
- تعليقات كود محسنة
- دليل التثبيت المفصل
- أمثلة الاستخدام

## [1.0.0] - 2025-01-25

### ✨ الإصدار الأولي
- **تسجيل الصوت** من الميكروفون
- **تحميل من YouTube** وروابط أخرى
- **تحويل الصيغ** بين MP3, WAV, OGG, FLAC
- **واجهة ثنائية اللغة** (عربية/إنجليزية)
- **عرض معلومات الملف** (المدة، الحجم، الصيغة)
- **شريط تقدم** للعمليات
- **إشعارات** عند اكتمال العمليات

### 🔧 المميزات التقنية
- مبني على PyQt5
- عمليات غير متزامنة مع QThread
- دعم sounddevice لتسجيل الصوت
- استخدام yt-dlp للتحميل
- pydub لتحويل الصيغ

---

## خطط المستقبل

### [2.1.0] - قريباً
- [ ] **معاينة الصوت** قبل الحفظ
- [ ] **تسجيل مجدول** مع مؤقت
- [ ] **فلاتر صوتية** (تقليل الضوضاء، تحسين الجودة)
- [ ] **تسجيل متعدد المصادر** في نفس الوقت
- [ ] **قوائم تشغيل** للتحميل المجمع

### [2.2.0] - المستقبل
- [ ] **واجهة ويب** للتحكم عن بعد
- [ ] **تطبيق موبايل** مصاحب
- [ ] **تخزين سحابي** مدمج
- [ ] **ذكاء اصطناعي** لتحسين جودة الصوت
- [ ] **تحليل طيفي** للصوت

---

## ملاحظات الترقية

### من 1.x إلى 2.0
1. قم بعمل نسخة احتياطية من إعداداتك
2. احذف المجلد القديم
3. قم بتحميل الإصدار الجديد
4. شغل `run.py` أو `run.bat`

### متطلبات جديدة في 2.0
- Python 3.6+ (بدلاً من 3.5+)
- مساحة إضافية 50MB للملفات الجديدة
- ffmpeg موصى به بشدة

---

## الدعم والمساهمة

- **الإبلاغ عن الأخطاء**: [GitHub Issues](https://github.com/audiorecorder/issues)
- **طلب مميزات**: [GitHub Discussions](https://github.com/audiorecorder/discussions)
- **المساهمة**: راجع [CONTRIBUTING.md](CONTRIBUTING.md)

---

## شكر خاص

- فريق PyQt5 للواجهة الرائعة
- مطوري yt-dlp للأداة القوية
- مجتمع Python للدعم المستمر
- جميع المستخدمين والمساهمين
