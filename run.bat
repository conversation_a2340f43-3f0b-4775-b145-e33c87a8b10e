@echo off
chcp 65001 >nul
title Audio Recorder Pro - مسجل الصوت المحترف

echo.
echo 🎵 Audio Recorder Pro - مسجل الصوت المحترف
echo ==================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo يرجى تثبيت pip أو إعادة تثبيت Python
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM التحقق من وجود ملف المتطلبات
if not exist requirements.txt (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo 📦 التحقق من المكتبات المطلوبة...
pip check >nul 2>&1

REM تثبيت المكتبات إذا لم تكن مثبتة
echo 📥 تثبيت/تحديث المكتبات المطلوبة...
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
    pause
    exit /b 1
)

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

REM تشغيل التطبيق
echo 🚀 بدء تشغيل التطبيق...
python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    pause
)

echo.
echo 👋 شكراً لاستخدام Audio Recorder Pro
pause
