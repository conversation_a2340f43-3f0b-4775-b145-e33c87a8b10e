#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبارات بسيطة لتطبيق Audio Recorder Pro
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class TestDependencies(unittest.TestCase):
    """اختبار توفر المكتبات المطلوبة"""

    def test_pyqt5_import(self):
        """اختبار استيراد PyQt5"""
        try:
            from PyQt5.QtWidgets import QApplication
            self.assertTrue(True, "PyQt5 متوفر")
        except ImportError:
            self.fail("PyQt5 غير متوفر")

    def test_sounddevice_import(self):
        """اختبار استيراد sounddevice"""
        try:
            import sounddevice
            self.assertTrue(True, "sounddevice متوفر")
        except ImportError:
            self.fail("sounddevice غير متوفر")

    def test_soundfile_import(self):
        """اختبار استيراد soundfile"""
        try:
            import soundfile
            self.assertTrue(True, "soundfile متوفر")
        except ImportError:
            self.fail("soundfile غير متوفر")

    def test_numpy_import(self):
        """اختبار استيراد numpy"""
        try:
            import numpy
            self.assertTrue(True, "numpy متوفر")
        except ImportError:
            self.fail("numpy غير متوفر")

    def test_yt_dlp_import(self):
        """اختبار استيراد yt-dlp"""
        try:
            import yt_dlp
            self.assertTrue(True, "yt-dlp متوفر")
        except ImportError:
            try:
                import youtube_dl
                self.assertTrue(True, "youtube-dl متوفر كبديل")
            except ImportError:
                self.fail("yt-dlp أو youtube-dl غير متوفر")

    def test_pydub_import(self):
        """اختبار استيراد pydub"""
        try:
            from pydub import AudioSegment
            self.assertTrue(True, "pydub متوفر")
        except ImportError:
            self.fail("pydub غير متوفر")

class TestConfigFiles(unittest.TestCase):
    """اختبار ملفات التكوين"""

    def test_config_file_exists(self):
        """اختبار وجود ملف config.py"""
        self.assertTrue(Path("config.py").exists(), "ملف config.py موجود")

    def test_utils_file_exists(self):
        """اختبار وجود ملف utils.py"""
        self.assertTrue(Path("utils.py").exists(), "ملف utils.py موجود")

    def test_main_file_exists(self):
        """اختبار وجود ملف main.py"""
        self.assertTrue(Path("main.py").exists(), "ملف main.py موجود")

    def test_requirements_file_exists(self):
        """اختبار وجود ملف requirements.txt"""
        self.assertTrue(Path("requirements.txt").exists(), "ملف requirements.txt موجود")

class TestUtilityFunctions(unittest.TestCase):
    """اختبار الدوال المساعدة"""

    def setUp(self):
        """إعداد الاختبارات"""
        try:
            from utils import format_duration, format_file_size, validate_url
            self.format_duration = format_duration
            self.format_file_size = format_file_size
            self.validate_url = validate_url
        except ImportError:
            self.skipTest("utils.py غير متوفر")

    def test_format_duration(self):
        """اختبار تنسيق المدة"""
        self.assertIn("ثانية", self.format_duration(30))
        self.assertIn("دقيقة", self.format_duration(90))
        self.assertIn("ساعة", self.format_duration(3700))

    def test_format_file_size(self):
        """اختبار تنسيق حجم الملف"""
        self.assertIn("بايت", self.format_file_size(500))
        self.assertIn("كيلوبايت", self.format_file_size(2048))
        self.assertIn("ميجابايت", self.format_file_size(2097152))

    def test_validate_url(self):
        """اختبار التحقق من صحة الروابط"""
        # روابط صحيحة
        self.assertTrue(self.validate_url("https://www.youtube.com/watch?v=dQw4w9WgXcQ"))
        self.assertTrue(self.validate_url("https://youtu.be/dQw4w9WgXcQ"))

        # روابط خاطئة
        self.assertFalse(self.validate_url("not_a_url"))
        self.assertFalse(self.validate_url(""))

class TestApplicationLaunch(unittest.TestCase):
    """اختبار تشغيل التطبيق"""

    @patch('sys.argv', ['test'])
    def test_app_import(self):
        """اختبار استيراد التطبيق"""
        try:
            from main import AudioRecorderApp
            self.assertTrue(True, "تم استيراد التطبيق بنجاح")
        except ImportError as e:
            self.fail(f"فشل في استيراد التطبيق: {e}")

    @patch('sys.argv', ['test'])
    def test_app_creation(self):
        """اختبار إنشاء التطبيق"""
        try:
            from PyQt5.QtWidgets import QApplication
            from main import AudioRecorderApp

            app = QApplication([])
            window = AudioRecorderApp()
            self.assertIsNotNone(window, "تم إنشاء النافذة بنجاح")
            app.quit()
        except Exception as e:
            self.fail(f"فشل في إنشاء التطبيق: {e}")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء تشغيل الاختبارات...")
    print("=" * 50)

    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()

    # إضافة اختبارات المكتبات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDependencies))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfigFiles))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestUtilityFunctions))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestApplicationLaunch))

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # عرض النتائج
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        print(f"تم تشغيل {result.testsRun} اختبار بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت:")
        print(f"نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"فشل: {len(result.failures)}")
        print(f"أخطاء: {len(result.errors)}")

    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
