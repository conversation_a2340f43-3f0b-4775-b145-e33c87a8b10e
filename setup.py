#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages
import os
from pathlib import Path

# قراءة محتوى README
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# قراءة المتطلبات
requirements = []
if os.path.exists('requirements.txt'):
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="audio-recorder-pro",
    version="2.1.0",
    author="Audio Recorder Team",
    author_email="<EMAIL>",
    description="تطبيق احترافي لتسجيل الصوت وتحميل الملفات الصوتية",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/audiorecorder/audio-recorder-pro",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Sound/Audio :: Capture/Recording",
        "Topic :: Multimedia :: Sound/Audio :: Conversion",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.6",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-qt>=4.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "audio": [
            "librosa>=0.8.0",
            "scipy>=1.6.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "audio-recorder=main:main",
            "audiorecorder=main:main",
        ],
        "gui_scripts": [
            "audio-recorder-gui=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["assets/*", "*.md", "*.txt", "*.cfg"],
    },
    data_files=[
        ("share/applications", ["assets/audio-recorder.desktop"]),
        ("share/pixmaps", ["assets/icon.png"]),
        ("share/doc/audio-recorder", ["README.md", "LICENSE"]),
    ],
    zip_safe=False,
    keywords="audio recorder sound recording youtube download converter",
    project_urls={
        "Bug Reports": "https://github.com/audiorecorder/audio-recorder-pro/issues",
        "Source": "https://github.com/audiorecorder/audio-recorder-pro",
        "Documentation": "https://audiorecorder.readthedocs.io/",
    },
)
