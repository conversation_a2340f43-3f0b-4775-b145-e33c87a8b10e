# 🤝 دليل المساهمة - Contributing Guide

نرحب بمساهماتكم في تطوير Audio Recorder Pro! هذا الدليل سيساعدكم على البدء.

## 📋 جدول المحتويات

- [كيفية المساهمة](#كيفية-المساهمة)
- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
- [معايير الكود](#معايير-الكود)
- [إرسال التحسينات](#إرسال-التحسينات)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [طلب المميزات](#طلب-المميزات)

## 🚀 كيفية المساهمة

### أنواع المساهمات المرحب بها

- 🐛 **إصلاح الأخطاء**: حل المشاكل الموجودة
- ✨ **مميزات جديدة**: إضافة وظائف جديدة
- 📚 **تحسين التوثيق**: تطوير الدليل والشرح
- 🌐 **الترجمة**: إضافة لغات جديدة
- 🎨 **تحسين التصميم**: تطوير الواجهة
- ⚡ **تحسين الأداء**: تسريع التطبيق

### قبل البدء

1. **تحقق من Issues الموجودة** لتجنب التكرار
2. **ناقش المميزات الكبيرة** في GitHub Discussions أولاً
3. **اقرأ هذا الدليل** كاملاً

## 🛠️ إعداد بيئة التطوير

### المتطلبات

- Python 3.6 أو أحدث
- Git
- محرر نصوص (VS Code موصى به)

### خطوات الإعداد

1. **Fork المشروع**:
   ```bash
   # انقر على Fork في GitHub
   ```

2. **استنساخ المشروع**:
   ```bash
   git clone https://github.com/YOUR_USERNAME/audio-recorder-pro.git
   cd audio-recorder-pro
   ```

3. **إنشاء بيئة افتراضية**:
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

4. **تثبيت المتطلبات**:
   ```bash
   pip install -r requirements.txt
   pip install -e .
   ```

5. **تثبيت أدوات التطوير**:
   ```bash
   pip install pytest pytest-qt black flake8 mypy
   ```

6. **اختبار التثبيت**:
   ```bash
   python test_app.py
   python main.py
   ```

## 📝 معايير الكود

### أسلوب الكتابة

- **استخدم Black** لتنسيق الكود:
  ```bash
  black *.py
  ```

- **اتبع PEP 8** لمعايير Python
- **أضف تعليقات باللغة العربية** للوضوح
- **استخدم أسماء متغيرات واضحة**

### بنية الكود

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وصف الملف والغرض منه
"""

import sys
import os
# استيرادات أخرى...

class MyClass:
    """وصف الكلاس"""
    
    def __init__(self):
        """منشئ الكلاس"""
        pass
    
    def my_method(self, param):
        """
        وصف الدالة
        
        Args:
            param: وصف المعامل
            
        Returns:
            وصف القيمة المرجعة
        """
        return result
```

### التعليقات والتوثيق

- **اكتب docstrings** لجميع الدوال والكلاسات
- **أضف تعليقات** للكود المعقد
- **استخدم العربية** في التعليقات
- **وثق المعاملات** والقيم المرجعة

## 🧪 الاختبارات

### تشغيل الاختبارات

```bash
# اختبارات أساسية
python test_app.py

# اختبارات متقدمة (إذا كانت متوفرة)
pytest tests/

# اختبار الواجهة
pytest --qt-no-capture tests/
```

### كتابة اختبارات جديدة

```python
import unittest
from unittest.mock import Mock, patch

class TestNewFeature(unittest.TestCase):
    """اختبار الميزة الجديدة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        pass
    
    def test_feature_works(self):
        """اختبار أن الميزة تعمل"""
        # كود الاختبار
        self.assertTrue(True)
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        pass
```

## 📤 إرسال التحسينات

### خطوات إرسال Pull Request

1. **إنشاء فرع جديد**:
   ```bash
   git checkout -b feature/new-feature
   # أو
   git checkout -b fix/bug-description
   ```

2. **إجراء التغييرات**:
   ```bash
   # عدل الملفات
   git add .
   git commit -m "وصف واضح للتغيير"
   ```

3. **اختبار التغييرات**:
   ```bash
   python test_app.py
   black *.py
   flake8 *.py
   ```

4. **رفع التغييرات**:
   ```bash
   git push origin feature/new-feature
   ```

5. **إنشاء Pull Request** في GitHub

### معايير Pull Request

- **عنوان واضح** يصف التغيير
- **وصف مفصل** للتغييرات
- **ربط بـ Issue** إذا كان موجوداً
- **اختبار التغييرات** قبل الإرسال
- **تحديث التوثيق** إذا لزم الأمر

### قالب Pull Request

```markdown
## وصف التغيير
وصف مختصر للتغييرات المقترحة

## نوع التغيير
- [ ] إصلاح خطأ
- [ ] ميزة جديدة
- [ ] تحسين الأداء
- [ ] تحديث التوثيق

## الاختبارات
- [ ] تم اختبار التغييرات محلياً
- [ ] تم تشغيل جميع الاختبارات
- [ ] تم إضافة اختبارات جديدة (إذا لزم)

## قائمة التحقق
- [ ] الكود يتبع معايير المشروع
- [ ] التعليقات واضحة ومفيدة
- [ ] التوثيق محدث
- [ ] لا توجد تحذيرات جديدة
```

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ

1. **تحقق من Issues الموجودة**
2. **جرب أحدث إصدار**
3. **اجمع معلومات النظام**

### قالب الإبلاغ عن خطأ

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '...'
3. مرر إلى '...'
4. شاهد الخطأ

## السلوك المتوقع
وصف ما كان يجب أن يحدث

## السلوك الفعلي
وصف ما حدث بالفعل

## لقطات الشاشة
إذا كان مناسباً، أضف لقطات شاشة

## معلومات النظام
- نظام التشغيل: [مثل Windows 10]
- إصدار Python: [مثل 3.9.0]
- إصدار التطبيق: [مثل 2.0.0]

## معلومات إضافية
أي معلومات أخرى مفيدة
```

## 💡 طلب المميزات

### قالب طلب ميزة

```markdown
## هل طلبك مرتبط بمشكلة؟
وصف واضح للمشكلة. مثال: أشعر بالإحباط عندما [...]

## وصف الحل المطلوب
وصف واضح ومختصر لما تريده

## وصف البدائل
وصف أي حلول أو مميزات بديلة فكرت فيها

## معلومات إضافية
أي معلومات أخرى أو لقطات شاشة حول طلب الميزة
```

## 🌐 الترجمة

### إضافة لغة جديدة

1. **أضف الترجمة** في `main.py`:
   ```python
   translations = {
       'ar': {...},
       'en': {...},
       'fr': {  # لغة جديدة
           'app_title': 'Enregistreur Audio',
           # باقي الترجمات...
       }
   }
   ```

2. **اختبر الترجمة** في التطبيق
3. **أرسل Pull Request**

## 🎨 تحسين التصميم

### تعديل الأنماط

الأنماط موجودة في `config.py`:

```python
STYLES = {
    'primary_button': """
        QPushButton {
            background-color: #2196F3;
            color: white;
            /* باقي الأنماط */
        }
    """
}
```

## 📞 التواصل

- **GitHub Issues**: للأخطاء والمشاكل
- **GitHub Discussions**: للنقاشات العامة
- **Pull Requests**: للمساهمات

## 🙏 شكر خاص

نشكر جميع المساهمين في تطوير هذا المشروع:

- المطورين الذين أضافوا مميزات
- المختبرين الذين أبلغوا عن أخطاء
- المترجمين الذين أضافوا لغات
- المستخدمين الذين قدموا اقتراحات

**شكراً لمساهمتكم في جعل Audio Recorder Pro أفضل! 🎵**
