#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مساعد تثبيت مكتبات Audio Recorder Pro
يساعد في تثبيت المكتبات المطلوبة لتشغيل جميع مميزات التطبيق
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """تثبيت حزمة واحدة"""
    print(f"📦 تثبيت {package_name}...")
    if description:
        print(f"   {description}")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package_name} بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت {package_name}")
            print(f"   خطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package_name}: {e}")
        return False

def check_package(package_name):
    """التحقق من وجود حزمة"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """الدالة الرئيسية"""
    print("🎵 Audio Recorder Pro - مساعد تثبيت المكتبات")
    print("=" * 50)
    
    # قائمة المكتبات الأساسية
    basic_packages = [
        ("PyQt5", "واجهة المستخدم الرسومية"),
        ("sounddevice", "تسجيل الصوت"),
        ("soundfile", "معالجة ملفات الصوت"),
        ("numpy", "معالجة البيانات"),
        ("yt-dlp", "تحميل من YouTube"),
        ("plyer", "الإشعارات")
    ]
    
    # قائمة المكتبات الاختيارية
    optional_packages = [
        ("SpeechRecognition", "تحويل الصوت إلى نص (سريع)"),
        ("pyaudio", "مطلوب لـ SpeechRecognition"),
        ("openai-whisper", "تحويل الصوت إلى نص (دقة عالية)"),
        ("torch", "مطلوب لـ Whisper"),
        ("torchaudio", "مطلوب لـ Whisper")
    ]
    
    print("\n🔍 فحص المكتبات الأساسية...")
    basic_missing = []
    for package, desc in basic_packages:
        if check_package(package.replace('-', '_')):
            print(f"✅ {package} - متوفر")
        else:
            print(f"❌ {package} - مفقود")
            basic_missing.append((package, desc))
    
    print("\n🔍 فحص المكتبات الاختيارية...")
    optional_missing = []
    for package, desc in optional_packages:
        check_name = package.replace('-', '_')
        if check_name == "openai_whisper":
            check_name = "whisper"
        
        if check_package(check_name):
            print(f"✅ {package} - متوفر")
        else:
            print(f"⚠️  {package} - مفقود (اختياري)")
            optional_missing.append((package, desc))
    
    # تثبيت المكتبات المفقودة
    if basic_missing:
        print(f"\n📦 تثبيت {len(basic_missing)} مكتبة أساسية مفقودة...")
        for package, desc in basic_missing:
            install_package(package, desc)
    
    if optional_missing:
        print(f"\n❓ هناك {len(optional_missing)} مكتبة اختيارية مفقودة")
        print("هذه المكتبات تفعل ميزة تحويل الصوت إلى نص")
        
        choice = input("\nهل تريد تثبيت مكتبات تحويل الصوت إلى نص؟ (y/n): ").lower()
        
        if choice in ['y', 'yes', 'نعم', 'ن']:
            print("\n📦 تثبيت مكتبات تحويل الصوت إلى نص...")
            
            # تثبيت SpeechRecognition أولاً (أسرع وأصغر)
            speech_packages = [p for p in optional_missing if p[0] in ['SpeechRecognition', 'pyaudio']]
            for package, desc in speech_packages:
                install_package(package, desc)
            
            # سؤال عن Whisper (أكبر حجماً)
            whisper_choice = input("\nهل تريد تثبيت Whisper أيضاً؟ (دقة أعلى لكن حجم أكبر) (y/n): ").lower()
            if whisper_choice in ['y', 'yes', 'نعم', 'ن']:
                whisper_packages = [p for p in optional_missing if p[0] in ['openai-whisper', 'torch', 'torchaudio']]
                for package, desc in whisper_packages:
                    install_package(package, desc)
    
    print("\n" + "=" * 50)
    print("🎉 انتهى تثبيت المكتبات!")
    
    # فحص نهائي
    print("\n🔍 فحص نهائي...")
    all_good = True
    
    for package, desc in basic_packages:
        if not check_package(package.replace('-', '_')):
            print(f"❌ {package} - لا يزال مفقود")
            all_good = False
    
    if all_good:
        print("✅ جميع المكتبات الأساسية متوفرة!")
        print("\n🚀 يمكنك الآن تشغيل التطبيق:")
        print("   python main.py")
        print("   أو")
        print("   python run.py")
    else:
        print("❌ بعض المكتبات الأساسية لا تزال مفقودة")
        print("يرجى تثبيتها يدوياً أو التحقق من اتصال الإنترنت")
    
    # معلومات إضافية
    print("\n💡 معلومات مفيدة:")
    print("- SpeechRecognition: يحتاج اتصال إنترنت للعمل")
    print("- Whisper: يعمل بدون إنترنت لكن يحتاج ~140MB للنموذج")
    print("- يمكن استخدام التطبيق بدون مكتبات تحويل الصوت")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
