#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء أيقونة بسيطة للتطبيق
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def create_simple_icon():
    """إنشاء أيقونة بسيطة للتطبيق"""
    
    if not PIL_AVAILABLE:
        print("❌ مكتبة PIL غير متوفرة")
        print("لإنشاء الأيقونة، قم بتثبيت Pillow:")
        print("pip install Pillow")
        return False
    
    # إنشاء صورة جديدة
    size = (256, 256)
    image = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # ألوان التطبيق
    primary_color = (33, 150, 243)  # أزرق
    secondary_color = (255, 193, 7)  # أصفر
    white = (255, 255, 255)
    
    # رسم الخلفية الدائرية
    margin = 20
    circle_bbox = [margin, margin, size[0] - margin, size[1] - margin]
    draw.ellipse(circle_bbox, fill=primary_color)
    
    # رسم الميكروفون
    mic_width = 60
    mic_height = 80
    mic_x = (size[0] - mic_width) // 2
    mic_y = (size[1] - mic_height) // 2 - 20
    
    # جسم الميكروفون
    mic_body = [
        mic_x, mic_y,
        mic_x + mic_width, mic_y + mic_height
    ]
    draw.rounded_rectangle(mic_body, radius=30, fill=white)
    
    # شبكة الميكروفون
    for i in range(3):
        line_y = mic_y + 20 + (i * 15)
        line_start = mic_x + 15
        line_end = mic_x + mic_width - 15
        draw.line([line_start, line_y, line_end, line_y], fill=primary_color, width=3)
    
    # قاعدة الميكروفون
    stand_width = 4
    stand_height = 30
    stand_x = (size[0] - stand_width) // 2
    stand_y = mic_y + mic_height
    
    draw.rectangle([
        stand_x, stand_y,
        stand_x + stand_width, stand_y + stand_height
    ], fill=white)
    
    # قاعدة الحامل
    base_width = 40
    base_height = 8
    base_x = (size[0] - base_width) // 2
    base_y = stand_y + stand_height
    
    draw.rounded_rectangle([
        base_x, base_y,
        base_x + base_width, base_y + base_height
    ], radius=4, fill=white)
    
    # رسم موجات الصوت
    wave_center_x = size[0] // 2 + 80
    wave_center_y = size[1] // 2
    
    for i in range(3):
        radius = 25 + (i * 15)
        thickness = 4 - i
        
        # رسم قوس للموجة
        bbox = [
            wave_center_x - radius, wave_center_y - radius,
            wave_center_x + radius, wave_center_y + radius
        ]
        
        draw.arc(bbox, start=-30, end=30, fill=secondary_color, width=thickness)
    
    # حفظ الأيقونة
    try:
        # حفظ بصيغ مختلفة
        image.save('assets/icon.png', 'PNG')
        
        # إنشاء أحجام مختلفة
        sizes = [16, 32, 48, 64, 128, 256]
        for size_px in sizes:
            resized = image.resize((size_px, size_px), Image.Resampling.LANCZOS)
            resized.save(f'assets/icon_{size_px}.png', 'PNG')
        
        # إنشاء ملف ICO للويندوز
        ico_sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
        ico_images = []
        for ico_size in ico_sizes:
            ico_img = image.resize(ico_size, Image.Resampling.LANCZOS)
            ico_images.append(ico_img)
        
        ico_images[0].save('assets/icon.ico', format='ICO', sizes=ico_sizes)
        
        print("✅ تم إنشاء الأيقونة بنجاح!")
        print("📁 الملفات المنشأة:")
        print("   - assets/icon.png (الأيقونة الرئيسية)")
        print("   - assets/icon_*.png (أحجام مختلفة)")
        print("   - assets/icon.ico (للويندوز)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الأيقونة: {e}")
        return False

def create_text_icon():
    """إنشاء أيقونة نصية بسيطة كبديل"""
    
    if not PIL_AVAILABLE:
        return False
    
    # إنشاء صورة بسيطة مع نص
    size = (256, 256)
    image = Image.new('RGBA', size, (33, 150, 243))  # خلفية زرقاء
    draw = ImageDraw.Draw(image)
    
    # محاولة استخدام خط
    try:
        font = ImageFont.truetype("arial.ttf", 60)
    except:
        try:
            font = ImageFont.truetype("DejaVuSans.ttf", 60)
        except:
            font = ImageFont.load_default()
    
    # رسم النص
    text = "AR"  # Audio Recorder
    
    # حساب موقع النص
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # رسم النص مع ظل
    shadow_offset = 3
    draw.text((x + shadow_offset, y + shadow_offset), text, font=font, fill=(0, 0, 0, 128))
    draw.text((x, y), text, font=font, fill=(255, 255, 255))
    
    # حفظ الأيقونة
    try:
        image.save('assets/icon_text.png', 'PNG')
        print("✅ تم إنشاء الأيقونة النصية!")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ الأيقونة النصية: {e}")
        return False

if __name__ == "__main__":
    print("🎨 إنشاء أيقونة التطبيق...")
    print("=" * 40)
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    import os
    os.makedirs('assets', exist_ok=True)
    
    # محاولة إنشاء الأيقونة المتقدمة
    if create_simple_icon():
        print("\n🎉 تم إنشاء جميع الأيقونات بنجاح!")
    else:
        # إنشاء أيقونة نصية كبديل
        print("\n🔄 محاولة إنشاء أيقونة بديلة...")
        if create_text_icon():
            print("✅ تم إنشاء أيقونة نصية بديلة")
        else:
            print("❌ فشل في إنشاء الأيقونة")
            print("يمكنك إضافة أيقونة يدوياً في مجلد assets/")
